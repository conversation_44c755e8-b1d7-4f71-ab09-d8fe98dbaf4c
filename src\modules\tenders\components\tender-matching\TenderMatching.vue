<script setup lang="ts">
import { computed, onBeforeMount, reactive, ref, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import CompaniesMatchingTable from '@/modules/tenders/components/CompaniesMatchingTable.vue';
import CompaniesMatchingProject from '@/modules/tenders/components/CompaniesMatchingProject.vue';
import MatchingTopCard from '@/modules/tenders/components/tender-matching/MatchingTopCard.vue';
import MatchingSkeleton from '@/modules/tenders/components/tender-matching/MatchingSkeleton.vue';
import { toast } from '@/common/utils/NotificationService';
import { CompaniesFilter, MatchingStatus } from '@/modules/tenders/types/tenders-enums';
import TenderInviteDrawer from '@/modules/tenders/components/TenderInviteDrawer.vue';
import MatchingLoadingText from '@/modules/tenders/components/MatchingLoadingText.vue';
import type { SortCriteria } from '@/workers/tender-sorting.worker';
import { cleanCloneObject } from '@/common/utils/objects';

const tenderStore = useTenderStore();
const route = useRoute();
const { t } = useI18n();

const loading = ref(true);
const invitedCompanyIds = computed(() => tenderStore.invitedVendorIds);
const expandedCompanyIds = ref<string[]>([]);
const matchingResponse = ref<any>(null);
const originalMatchingResponse = ref<any>(null);

const inviteVisible = ref(false);

const selectedSort = ref<SortCriteria>('overall_score');
const sortingWorker = new Worker(new URL('@/workers/tender-sorting.worker.ts', import.meta.url), {
  type: 'module',
});
const isSorting = ref(false);

const sortOptions = computed(() => [
  {
    value: 'overall_score' as SortCriteria,
    label: t('tenders.vendor-matching.sorting.nordics-score'),
    description: t('tenders.vendor-matching.sorting.nordics-score-description'),
    selectedBg: 'bg-blue-500 text-white'
  },
  {
    value: 'technologies_score' as SortCriteria,
    label: t('tenders.vendor-matching.sorting.tech-fit'),
    description: t('tenders.vendor-matching.sorting.tech-fit-description'),
    selectedBg: 'bg-green-500 text-white'
  },
  {
    value: 'projects_score' as SortCriteria,
    label: t('tenders.vendor-matching.sorting.domain-fit'),
    description: t('tenders.vendor-matching.sorting.domain-fit-description'),
    selectedBg: 'bg-purple-500 text-white'
  }
]);

const currentSortDescription = computed(() => {
  const option = sortOptions.value.find(opt => opt.value === selectedSort.value);
  return option?.description ?? '';
});

const topCompanies = computed(() => matchingResponse.value?.topVendors ?? matchingResponse.value?.companies ?? []);
const otherCompanies = computed(() => matchingResponse.value?.otherVendors ?? []);
const dataReady = computed(() => topCompanies.value.length > 0 || otherCompanies.value.length > 0);
const matchId = computed(() => tenderStore.specificMatchingResult?.id ?? '');
const matchingStatus = computed(() => tenderStore.matchingStatus[CompaniesFilter.Selection]);

const projectDetailModal = reactive({
  isOpened: false,
  detail: undefined as any,
});

const emit = defineEmits(['matching-loaded']);

const initializeSortingWorker = () => {
  sortingWorker.onmessage = event => {
    const { sortedCompanies, criteria } = event.data;

    if (criteria === selectedSort.value) {
      const allCompanies = [...(originalMatchingResponse.value?.topVendors ?? []), ...(originalMatchingResponse.value?.otherVendors ?? [])];
      const sortedAllCompanies = sortedCompanies.filter((company: any) =>
        allCompanies.some(original => original.id === company.id)
      );

      // Split back into top and other companies based on original structure
      const topCount = originalMatchingResponse.value?.topVendors?.length ?? 0;
      const sortedTopVendors = sortedAllCompanies.slice(0, topCount);
      const sortedOtherVendors = sortedAllCompanies.slice(topCount);

      matchingResponse.value = {
        ...originalMatchingResponse.value,
        topVendors: sortedTopVendors,
        otherVendors: sortedOtherVendors,
        companies: sortedTopVendors
      };
    }

    isSorting.value = false;
  };

  sortingWorker.onerror = error => {
    console.error('Sorting worker error:', error);
    isSorting.value = false;
    toast.show('Error', 'Something went wrong with sorting.', 'error');
  };
};

const sortCompanies = (criteria: SortCriteria) => {
  if (!originalMatchingResponse.value || isSorting.value) {
    return;
  }

  selectedSort.value = criteria;
  isSorting.value = true;

  const allCompanies = [
    ...(originalMatchingResponse.value.topVendors ?? []),
    ...(originalMatchingResponse.value.otherVendors ?? [])
  ];

  sortingWorker.postMessage(cleanCloneObject(
    {
      companies: allCompanies,
      criteria
    }
  ));
};

const handleCompanyInvite = async(id: string) => {
  try {
    const tenderId = route.params.id as string;
    await tenderStore.inviteCompany(tenderId, id);
  } catch {
    toast.show('Error', 'Something went wrong with company invitation.', 'error');
  }
};

const handleCompanyUninvite = async(id: string) => {
  try {
    const tenderId = route.params.id as string;
    await tenderStore.uninviteCompany(tenderId, id);
  } catch {
    toast.show('Error', 'Something went wrong with company uninvitation.', 'error');
  }
};

const toggleCompanyExpanded = (id: string) => {
  if (expandedCompanyIds.value.includes(id)) {
    expandedCompanyIds.value = expandedCompanyIds.value.filter(c => c !== id);
  } else {
    expandedCompanyIds.value.push(id);
  }
};

const onShowProjectDetail = (companyId: number) => {
  const company = [...topCompanies.value, ...otherCompanies.value].find(c => c?.id === companyId);
  if (!company) {
    toast.show('Company not found', 'We were not able to find the requested company.', 'error');
    return;
  }

  projectDetailModal.detail = {
    id: company.id,
    name: company.name,
    country: company.country ?? '',
    created_at: company.created_at ?? '',
    about: company.about ?? '',
    industries: company.industries ?? [],
    technologies: company.technologies ?? [],
    matching: company.matching ?? { score: 0, tech_score: 0 },
  };

  projectDetailModal.isOpened = true;
};

onBeforeMount(async() => {
  initializeSortingWorker();
  const id = route.params.id as string;
  const [matching] = await Promise.all([
    tenderStore.startMatchingPoll(id, 5000, CompaniesFilter.Selection),
    tenderStore.fetchInvitedCompanies(id),
  ]);
  originalMatchingResponse.value = matching;
  matchingResponse.value = matching;
  loading.value = false;
  emit('matching-loaded');
});

onBeforeUnmount(() => {
  sortingWorker.terminate();
});
</script>

<template>
  <div v-if="matchingStatus === MatchingStatus.InProgress" class="min-h-[250px] flex items-center justify-center">
    <MatchingLoadingText />
  </div>
  <div v-else class="w-full transition-all duration-500 ease-[var(--ease-out-3)]">
    <div
      v-if="!loading && dataReady"
      class="mb-6"
      role="group"
      aria-label="Sorting options"
    >
      <div class="flex items-center gap-2 flex-wrap mb-4">
        <span class="text-sm text-gray-600">Sort by:</span>
        <div class="flex gap-2 flex-wrap">
          <button
            v-for="option in sortOptions"
            :key="option.value"
            class="px-3 py-1.5 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer"
            :class="selectedSort === option.value
              ? (option.selectedBg ?? 'bg-blue-500 text-white')
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
            :disabled="isSorting"
            @click="sortCompanies(option.value)"
          >
            {{ option.label }}
          </button>
        </div>
      </div>

      <div v-if="currentSortDescription" class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
        {{ currentSortDescription }}
      </div>
    </div>

    <transition mode="out-in" :duration="400">
      <div v-if="matchingStatus === MatchingStatus.Failed || matchingStatus === MatchingStatus.NoVendorsMatched">
        <MatchingSkeleton :error="true" :matching-status="matchingStatus" />
      </div>
      <div v-else-if="!loading && dataReady" class="grid gap-4 w-full xl:grid-cols-2">
        <MatchingTopCard
          v-for="(company, index) in topCompanies"
          :key="company?.id"
          :company="company"
          :index="index"
          :tender-id="route.params.id as string"
          :invited="invitedCompanyIds.includes(company.id)"
          @invite="handleCompanyInvite"
          @uninvite="handleCompanyUninvite"
        />
      </div>
      <MatchingSkeleton v-else />
    </transition>

    <div v-if="otherCompanies?.length > 0" class="mt-5">
      <CompaniesMatchingTable
        :companies="otherCompanies"
        :starting-index="topCompanies.length + 1"
        :size="5"
        :tender-public-id="route.params.id as string"
        :match-id="matchId"
        type="top"
        :loading="loading"
        :expanded-company-ids="expandedCompanyIds"
        :invited-company-ids="invitedCompanyIds"
        @invite="handleCompanyInvite"
        @uninvite="handleCompanyUninvite"
        @show-project-detail="onShowProjectDetail"
        @toggle-expand="toggleCompanyExpanded"
      />
    </div>

    <CompaniesMatchingProject
      v-model="projectDetailModal.isOpened"
      :project-detail="projectDetailModal.detail"
    />

    <TenderInviteDrawer v-model:visible="inviteVisible" />
  </div>
</template>
