import { type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class TenderPage extends BasePage {
  readonly header: Locator;
  readonly submissionDeadline: Locator;
  readonly submissionDeadlineEditButton: Locator;
  readonly invitesContainer: Locator;
  readonly strategicPartnersContainer: Locator;
  readonly marketplaceContainer: Locator;

  constructor(page: Page) {
    super(page);
    this.header = page.getByRole('group', { name: 'Tender header' });
    this.submissionDeadline = this.header.getByLabel('Submission deadline');
    this.submissionDeadlineEditButton = this.header.getByRole('button', { name: 'Edit submission deadline' });
    this.invitesContainer = this.page.getByRole('group', { name: 'Invites' });
    this.strategicPartnersContainer = this.page.getByRole('group', { name: 'Strategic partners' });
    this.marketplaceContainer = this.page.getByRole('group', { name: 'Marketplace container' });
  }
}
