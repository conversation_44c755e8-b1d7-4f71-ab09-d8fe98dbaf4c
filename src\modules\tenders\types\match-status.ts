// types/match-status.
import { type Company as TenderCompany } from './tenders-types';

export type MatchStatusState = 'in_progress' | 'completed'

export interface MatchStatusResponse {
  data: MatchStatus[]
}

export interface Employees {
  employees: number
  developers: number
  positions: unknown[]
}

export interface Company {
  id: string
  name: string
  country: string
  headquarters: string
  website: string
  linkedin: string
  about: string
  founded_at: number
  cover: string | null
  logo: string | null
  main_industry: string
  industries: string[]
  technologies: string[]
  clients: unknown[]
  employees: Employees
  rates: {
    juniors: number
    mediors: number
    seniors: number
    leads: number
  }
  overall_score: number
  technologies_score: number
  projects_score: number
  location_match: string
  matching_rates: string
}

export interface MatchStatus {
  id: string;
  status: string;
  companies: TenderCompany[];
  topVendors?: any[];
  otherVendors?: any[];
}
