interface Company {
  id: string;
  name: string;
  match_details?: {
    overall_score?: number;
    projects_score?: number;
    technologies_score?: number;
  };
  [key: string]: any;
}

export type SortCriteria = 'overall_score' | 'technologies_score' | 'projects_score';

interface SortRequest {
  companies: Company[];
  criteria: SortCriteria;
}

interface SortResponse {
  sortedCompanies: Company[];
  criteria: SortCriteria;
}

self.onmessage = ({ data }: MessageEvent<SortRequest>) => {
  const { companies, criteria } = data;

  if (!companies || !Array.isArray(companies)) {
    self.postMessage({
      sortedCompanies: [],
      criteria,
    } as SortResponse);
    return;
  }

  try {
    const companiesCopy = [...companies];
    const sortedCompanies = companiesCopy.sort((a, b) => {
      const scoreA = a.match_details?.[criteria] ?? 0;
      const scoreB = b.match_details?.[criteria] ?? 0;
      return scoreB - scoreA;
    });

    self.postMessage({
      sortedCompanies,
      criteria,
    } as SortResponse);
  } catch (error) {
    console.error('Error sorting companies:', error);
    self.postMessage({
      sortedCompanies: companies,
      criteria,
    } as SortResponse);
  }
};

export {};
