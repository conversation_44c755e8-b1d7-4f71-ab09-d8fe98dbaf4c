interface Company {
  id: string;
  name: string;
  match_details?: {
    overall_score?: number;
    projects_score?: number;
    technologies_score?: number;
  };
  [key: string]: any;
}

export type SortCriteria = 'overall_score' | 'technologies_score' | 'projects_score';

interface SortRequest {
  topVendors: Company[];
  otherVendors: Company[];
  criteria: SortCriteria;
}

interface SortResponse {
  sortedTopVendors: Company[];
  sortedOtherVendors: Company[];
  criteria: SortCriteria;
}

self.onmessage = ({ data }: MessageEvent<SortRequest>) => {
  const { topVendors, otherVendors, criteria } = data;

  // Validate input data
  if (!Array.isArray(topVendors) || !Array.isArray(otherVendors)) {
    self.postMessage({
      sortedTopVendors: [],
      sortedOtherVendors: [],
      criteria,
    } as SortResponse);
    return;
  }

  try {
    const allCompanies = [...topVendors, ...otherVendors];
    const sortedAllCompanies = allCompanies.sort((a, b) => {
      const scoreA = a.match_details?.[criteria] ?? 0;
      const scoreB = b.match_details?.[criteria] ?? 0;
      return scoreB - scoreA;
    });

    // Split back into top and other companies based on original structure
    const topCount = topVendors.length;
    const sortedTopVendors = sortedAllCompanies.slice(0, topCount);
    const sortedOtherVendors = sortedAllCompanies.slice(topCount);

    self.postMessage({
      sortedTopVendors,
      sortedOtherVendors,
      criteria,
    } as SortResponse);
  } catch (error) {
    console.error('Error sorting companies:', error);
    self.postMessage({
      sortedTopVendors: topVendors,
      sortedOtherVendors: otherVendors,
      criteria,
    } as SortResponse);
  }
};

export {};
