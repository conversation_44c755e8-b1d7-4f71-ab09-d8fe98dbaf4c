import { expect, test, Page } from '@playwright/test';
import { TendersPage } from './page-objects/tenders/TendersPage';
import { TenderPage } from './page-objects/tenders/TenderPage';
import { DateTime } from 'luxon';

const tender = {
  id: 'Yd0CGJ',
  name: '.NET Developer for Logistics Project',
  description: 'Project Overview This capacity will be responsible for software development activities under a workstream managed by DHL...',
  positions: ['Boss Coder',],
  location: 'Czechia, Slovakia',
  category: 'Food',
  candidate: {
    name: '<PERSON>',
    vendor: 'Vendor company',
    position: 'Boss Coder',
    profession: 'Software Engineer',
    seniority: 'senior',
    residence: 'Poland',
    rate: '€40/hr',
    technologies: ['.NET Development', 'C# (Programming Language)', 'Angular (Web Framework)'],
  }
};

const getNewDate = () => {
  const newDate = DateTime.now().plus({ days: 30 });
  return {
    newDateISO: newDate.toFormat("yyyy-MM-dd'T'HH:mm"),
    newDateFormatted: newDate.toFormat('d LLLL yyyy'),
  };
};

const openSampleTender = async(page: Page) => {
  const tendersPage = new TendersPage(page);
  await tendersPage.goto();
  await tendersPage.openTender(tender.name);
};

test('Check Tender information', async({ page }) => {
  await openSampleTender(page);

  const tenderPage = new TenderPage(page);

  await expect(tenderPage.header).toBeVisible();

  await expect(page.getByText(/By/)).toContainText(/@Ignác.*/);

  await expect(page.getByText(tender.location)).toBeVisible();
  await expect(page.getByText(tender.category)).toBeVisible();

  for (const position of tender.positions) {
    await expect(page.getByText(position)).toBeVisible();
  }

  const tenderName = page.getByRole('heading', { name: tender.name });
  await expect(tenderName).toContainText(tender.name);
  await expect(page.getByText(tender.description)).toBeVisible();

  await expect(page.getByRole('link', { name: 'Marketplace' })).toBeVisible();
  await expect(tenderPage.marketplaceContainer).toBeVisible();
});

test('Error: tender with wrong id', async({ page }) => {
  await page.goto('/tenders/Yd0CG/detail');
  await expect(page).toHaveURL('/tenders');
});

test('Search strategic partners', async({ page }) => {
  await openSampleTender(page);

  const tenderPage = new TenderPage(page);
  await expect(tenderPage.strategicPartnersContainer).toBeVisible();
  const companiesList = tenderPage.strategicPartnersContainer.getByRole('list', { name: 'Companies list' });
  await expect(companiesList).toBeVisible({ timeout: 15000 });
  let count = await companiesList.getByRole('listitem').count();
  expect(count).toBeGreaterThan(0);
  await tenderPage.strategicPartnersContainer.getByRole('textbox', { name: 'search' }).fill('Vendor company');
  await expect(companiesList.getByRole('listitem')).toBeVisible();
  count = await companiesList.getByRole('listitem').count();
  expect(count).toBe(1);
  await expect(companiesList.getByRole('listitem').first()).toContainText('Vendor company');
});

test('See and edit submission deadline in tender header', async({ page }) => {
  await openSampleTender(page);

  const tenderPage = new TenderPage(page);
  await expect(tenderPage.submissionDeadline).toBeVisible();
  await expect(tenderPage.submissionDeadlineEditButton).toBeVisible();
  await tenderPage.submissionDeadlineEditButton.click();
  const submissionDeadlineModal = page.getByRole('dialog', { name: 'Update Submission Deadline' });
  await expect(submissionDeadlineModal).toBeVisible();

  const { newDateISO, newDateFormatted } = getNewDate();

  await submissionDeadlineModal
    .getByRole('textbox')
    .fill(newDateISO);
  await submissionDeadlineModal.getByRole('button', { name: 'Submit' }).click();
  await expect(tenderPage.submissionDeadline).toContainText(newDateFormatted);
});

test('Show submission deadline in invite modal and edit', async({ page }) => {
  await openSampleTender(page);

  const tenderPage = new TenderPage(page);
  await tenderPage.invitesContainer.getByRole('button', { name: 'Invite vendors' }).click();
  const invitesContainer = page.getByRole('complementary');
  await expect(invitesContainer).toBeVisible();
  await expect(invitesContainer).toContainText('Invites');

  await invitesContainer.getByRole('button', { name: 'Add Strategic Partners' }).click();
  await invitesContainer.getByRole('list', { name: 'Strategic partners' }).getByRole('button', { name: 'Invite' }).first().click();
  await invitesContainer.getByRole('button', { name: 'Send invites' }).click();

  const emailInviteModal = page.getByRole('dialog', { name: 'Email invite modal' });
  await expect(emailInviteModal).toBeVisible();
  await expect(emailInviteModal.getByRole('heading', { name: 'Update Submission Deadline' })).toBeVisible();

  const { newDateISO, newDateFormatted } = getNewDate();
  await emailInviteModal
    .getByRole('textbox')
    .fill(newDateISO);
  await emailInviteModal.getByRole('button', { name: 'Next' }).click();
  await emailInviteModal.getByRole('button', { name: 'Close' }).click();

  await expect(tenderPage.submissionDeadline).toContainText(newDateFormatted);
});

test('Load Vendor Matching page', async({ page }) => {
  await openSampleTender(page);
  await page.getByRole('link', { name: 'Vendor Matching' }).click();
  await expect(page).toHaveURL(`/tenders/${tender.id}/matching/vendors`);
});

test('Load Marketplace page', async({ page }) => {
  await openSampleTender(page);
  await page.getByRole('link', { name: 'Marketplace' }).click();
  await expect(page).toHaveURL(`/tenders/${tender.id}/matching/marketplace`);

});

test('Load Proposals page', async({ page }) => {
  await openSampleTender(page);
  await page.getByRole('link', { name: 'Proposals' }).click();
  await expect(page).toHaveURL(`/tenders/${tender.id}/proposal`);
});

test('Check candidate information', async({ page }) => {
  page.goto(`/tenders/${tender.id}/proposal`);

  await expect(page.getByText(tender.candidate.name)).toBeVisible();
  await expect(page.getByText(tender.candidate.position)).toBeVisible();
  await expect(page.getByText(tender.candidate.vendor).nth(1)).toBeVisible();
  await expect(page.getByText(tender.candidate.seniority, { exact: true })).toBeVisible();
  await expect(page.getByText(tender.candidate.rate)).toBeVisible();

  await page.getByText(tender.candidate.vendor).nth(1).click();
  await expect(page.getByText(tender.candidate.profession)).toBeVisible();
  await expect(page.getByText(tender.candidate.residence)).toBeVisible();

  for (const technologies of tender.candidate.technologies) {
    await expect(page.getByText(technologies)).toBeVisible();
  }
});

test('Check candidate status display and change status', async({ page }) => {
  await page.goto(`/tenders/${tender.id}/proposal`);

  // Check initial display
  await expect(page.getByTestId('candidates-table')).toBeVisible();
  await expect(page.getByText('Jane')).toBeVisible();
  await expect(page.getByRole('button', { name: 'Edit status' })).toBeVisible();

  // Check initial status value
  const candidateStatus = page.getByTestId('candidate-status');
  await expect(candidateStatus).toBeVisible();
  await expect(candidateStatus).toHaveAttribute('aria-label', 'New');
  await expect(candidateStatus).toHaveText('New');

  // Change status to Hired
  await page.getByRole('button', { name: 'Edit status' }).click();

  const statusModal = page.getByRole('dialog', { name: 'Update Status' });
  await expect(statusModal).toBeVisible();
  await expect(statusModal.getByText('New')).toBeVisible();

  await statusModal.getByTestId('dropdown-arrow').click();
  await statusModal.locator('li').filter({ hasText: 'Hired' }).click();
  await statusModal.getByTestId('submit-status-button').click();

  await expect(statusModal).not.toBeVisible();

  // Check status after change - both text and aria-label
  await expect(candidateStatus).toHaveAttribute('aria-label', 'Hired');
  await expect(candidateStatus).toHaveText('Hired');

  // Revert status back to New
  await page.getByRole('button', { name: 'Edit status' }).click();
  await expect(statusModal).toBeVisible();

  await statusModal.getByTestId('dropdown-arrow').click();
  await statusModal.locator('li').filter({ hasText: 'New' }).click();
  await statusModal.getByTestId('submit-status-button').click();

  await expect(statusModal).not.toBeVisible();

  // Check final status - back to New
  await expect(candidateStatus).toHaveAttribute('aria-label', 'New');
  await expect(candidateStatus).toHaveText('New');
});

test.skip('Don\'t show marketplace if disabled', async({ page }) => {
  // TODO: Enable when we have a way to disable marketplace for a workspace.
  await openSampleTender(page);
  const tenderPage = new TenderPage(page);
  await expect(tenderPage.header).toBeVisible();
  await expect(page.getByRole('link', { name: 'Marketplace' })).toBeHidden();
  await expect(tenderPage.marketplaceContainer).toBeHidden();
});
